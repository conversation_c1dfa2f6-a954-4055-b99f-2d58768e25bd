/* 表单相关样式 - 现代化设计 */
.query-form {
    background: rgba(248, 249, 250, 0.6);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 16px;
    margin-bottom: 30px;
    border: 1px solid rgba(233, 236, 239, 0.5);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.query-form:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.form-item {
    flex: 1;
    min-width: 200px;
}

/* 表单标签样式 */
.el-form-item__label {
    color: #2c3e50 !important;
    font-weight: 600 !important;
    font-size: 0.95em !important;
    margin-bottom: 8px !important;
}

/* 修复表单项多余背景条问题 */
.search-input-form-item {
    margin-bottom: 0 !important;
}

.search-input-form-item .el-form-item__content {
    position: relative !important;
    line-height: normal !important;
}

.search-input-form-item .el-form-item__content::before,
.search-input-form-item .el-form-item__content::after {
    display: none !important;
}

.search-input-form-item .el-form-item__error {
    display: none !important;
}

/* 移除所有可能的伪元素背景 */
.search-input-form-item::before,
.search-input-form-item::after {
    display: none !important;
}

/* 确保表单项没有额外的背景或边框 */
.search-input-form-item {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* 搜索输入框特殊样式 */
.search-input .el-input__wrapper {
    border-radius: 12px !important;
    border: 2px solid #e9ecef !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
}

.search-input .el-input__wrapper:hover {
    border-color: #667eea !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15) !important;
    transform: translateY(-1px) !important;
}

.search-input .el-input__wrapper.is-focus {
    border-color: #667eea !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.25) !important;
    transform: translateY(-1px) !important;
}

/* 修复可能的错误状态样式 */
.search-input .el-input__wrapper.is-error,
.search-input .el-input__wrapper.is-invalid {
    border-color: #667eea !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15) !important;
}

/* 输入框内部样式 */
.search-input .el-input__inner {
    color: #2c3e50 !important;
    font-weight: 500 !important;
    font-size: 0.95em !important;
}

.search-input .el-input__inner::placeholder {
    color: #6c757d !important;
    font-weight: 400 !important;
}

/* 输入框前缀图标 */
.search-input .el-input__prefix {
    color: #667eea !important;
}

/* 输入框后缀样式 */
.search-input .el-input__suffix {
    color: #6c757d !important;
}

.search-input .el-input__suffix:hover {
    color: #667eea !important;
}

/* 输入框组合样式 */
.search-input .el-input-group__append {
    background: rgba(102, 126, 234, 0.1) !important;
    border: 2px solid #667eea !important;
    border-left: none !important;
    border-radius: 0 12px 12px 0 !important;
    color: #667eea !important;
    transition: all 0.3s ease !important;
}

.search-input .el-input-group__append:hover {
    background: rgba(102, 126, 234, 0.2) !important;
    color: #5a6fd8 !important;
}

/* 查询按钮行的特殊样式 */
.form-row:last-child {
    margin-top: 10px;
    padding-top: 15px;
    border-top: 1px solid rgba(233, 236, 239, 0.5);
    justify-content: center;
}

/* 其他输入框和选择器样式 */
.el-select .el-input .el-input__wrapper {
    border-radius: 12px !important;
    border: 2px solid #e9ecef !important;
    transition: all 0.3s ease !important;
}

.el-select .el-input .el-input__wrapper:hover {
    border-color: #667eea !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1) !important;
}

.el-select .el-input .el-input__wrapper.is-focus {
    border-color: #667eea !important;
    box-shadow: 0 2px 12px rgba(102, 126, 234, 0.2) !important;
}

/* 开关样式 */
.el-switch {
    --el-switch-on-color: #667eea !important;
    --el-switch-off-color: #dcdfe6 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .query-form {
        padding: 20px;
        margin: 0 -10px 20px;
        border-radius: 12px;
    }

    .form-row {
        gap: 15px;
        margin-bottom: 15px;
    }

    .form-item {
        min-width: 150px;
    }

    .search-input .el-input__wrapper {
        border-radius: 10px !important;
    }
}

@media (max-width: 480px) {
    .query-form {
        padding: 15px;
        margin: 0 -15px 15px;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .form-item {
        min-width: auto;
        width: 100%;
    }
}
/* 专门修复表单验证和样式问题的CSS文件 */

/* 完全重置Element Plus表单验证状态 */
.el-form-item.is-error,
.el-form-item.is-success,
.el-form-item.is-validating,
.el-form-item.is-required {
    margin-bottom: 18px !important;
}

/* 移除所有表单验证相关的伪元素 */
.el-form-item::before,
.el-form-item::after,
.el-form-item.is-error::before,
.el-form-item.is-error::after,
.el-form-item.is-success::before,
.el-form-item.is-success::after,
.el-form-item.is-validating::before,
.el-form-item.is-validating::after {
    display: none !important;
    content: none !important;
}

/* 移除表单项内容区域的伪元素 */
.el-form-item__content::before,
.el-form-item__content::after {
    display: none !important;
    content: none !important;
}

/* 隐藏所有错误信息 */
.el-form-item__error {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 特别针对搜索输入框表单项的修复 */
.search-input-form-item,
.search-input-form-item.is-error,
.search-input-form-item.is-success,
.search-input-form-item.is-validating {
    margin-bottom: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.search-input-form-item::before,
.search-input-form-item::after,
.search-input-form-item.is-error::before,
.search-input-form-item.is-error::after,
.search-input-form-item.is-success::before,
.search-input-form-item.is-success::after,
.search-input-form-item.is-validating::before,
.search-input-form-item.is-validating::after {
    display: none !important;
    content: none !important;
}

.search-input-form-item .el-form-item__content,
.search-input-form-item.is-error .el-form-item__content,
.search-input-form-item.is-success .el-form-item__content,
.search-input-form-item.is-validating .el-form-item__content {
    position: relative !important;
    line-height: normal !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.search-input-form-item .el-form-item__content::before,
.search-input-form-item .el-form-item__content::after,
.search-input-form-item.is-error .el-form-item__content::before,
.search-input-form-item.is-error .el-form-item__content::after,
.search-input-form-item.is-success .el-form-item__content::before,
.search-input-form-item.is-success .el-form-item__content::after,
.search-input-form-item.is-validating .el-form-item__content::before,
.search-input-form-item.is-validating .el-form-item__content::after {
    display: none !important;
    content: none !important;
}

/* 确保搜索输入框本身的样式不受影响 */
.search-input-form-item .search-input,
.search-input-form-item.is-error .search-input,
.search-input-form-item.is-success .search-input,
.search-input-form-item.is-validating .search-input {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

/* 重置所有可能的表单状态样式 */
.el-form-item.is-error .el-input__wrapper,
.el-form-item.is-success .el-input__wrapper,
.el-form-item.is-validating .el-input__wrapper {
    border-color: #dcdfe6 !important;
    box-shadow: none !important;
}

/* 强制重置搜索输入框的所有状态 */
.search-input-form-item .search-input .el-input__wrapper,
.search-input-form-item.is-error .search-input .el-input__wrapper,
.search-input-form-item.is-success .search-input .el-input__wrapper,
.search-input-form-item.is-validating .search-input .el-input__wrapper {
    border: 2px solid #e9ecef !important;
    background: rgba(255, 255, 255, 0.9) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    border-radius: 12px !important;
}

.search-input-form-item .search-input .el-input__wrapper:hover,
.search-input-form-item.is-error .search-input .el-input__wrapper:hover,
.search-input-form-item.is-success .search-input .el-input__wrapper:hover,
.search-input-form-item.is-validating .search-input .el-input__wrapper:hover {
    border-color: #667eea !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15) !important;
}

.search-input-form-item .search-input .el-input__wrapper.is-focus,
.search-input-form-item.is-error .search-input .el-input__wrapper.is-focus,
.search-input-form-item.is-success .search-input .el-input__wrapper.is-focus,
.search-input-form-item.is-validating .search-input .el-input__wrapper.is-focus {
    border-color: #667eea !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.25) !important;
}

/* 移除任何可能的底部边框或背景条 */
.search-input-form-item .el-form-item__label + .el-form-item__content::after {
    display: none !important;
}

/* 确保表单项容器没有多余的样式 */
.form-item .el-form-item {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    margin-bottom: 0 !important;
}

.form-item .el-form-item::before,
.form-item .el-form-item::after {
    display: none !important;
}

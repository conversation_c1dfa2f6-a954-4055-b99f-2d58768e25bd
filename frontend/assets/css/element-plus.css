/* Element Plus 组件自定义样式 */
/* 仅对非搜索框的输入框应用样式 */
.el-input__wrapper:not(.search-input-wrapper) {
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    transition: all 0.3s ease !important;
}

.el-input__wrapper:not(.search-input-wrapper):hover {
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
}

.el-input__wrapper:not(.search-input-wrapper).is-focus {
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25) !important;
}

.el-select .el-input .el-input__wrapper {
    border-radius: 12px !important;
}

/* 修复表单项底部多余背景条问题 */
.el-form-item {
    margin-bottom: 18px !important;
}

.el-form-item__content {
    position: relative !important;
}

.el-form-item__error {
    display: none !important;
}

/* 移除表单验证状态的视觉效果 */
.el-form-item.is-error,
.el-form-item.is-success,
.el-form-item.is-validating {
    margin-bottom: 18px !important;
}

.el-form-item.is-error::after,
.el-form-item.is-success::after,
.el-form-item.is-validating::after {
    display: none !important;
}

/* 特别针对搜索输入框的表单项 */
.search-input-form-item .el-form-item__content::after {
    display: none !important;
}

/* 强制重置表单验证样式 */
.el-form-item.is-error .el-input__wrapper,
.el-form-item.is-success .el-input__wrapper,
.el-form-item.is-validating .el-input__wrapper {
    border-color: #dcdfe6 !important;
    box-shadow: none !important;
}

/* 特别针对搜索输入框的重置 */
.search-input.el-input .el-input__wrapper,
.el-form-item .search-input .el-input__wrapper,
.el-form-item.is-error .search-input .el-input__wrapper,
.el-form-item.is-success .search-input .el-input__wrapper,
.el-form-item.is-validating .search-input .el-input__wrapper {
    border: 2px solid #e9ecef !important;
    background: rgba(255, 255, 255, 0.9) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.search-input.el-input .el-input__wrapper:hover,
.el-form-item .search-input .el-input__wrapper:hover {
    border-color: #667eea !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15) !important;
}

.search-input.el-input .el-input__wrapper.is-focus,
.el-form-item .search-input .el-input__wrapper.is-focus {
    border-color: #667eea !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.25) !important;
}

.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-button--primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
}

.el-button--primary:active {
    transform: translateY(0) !important;
}

.el-switch.is-checked .el-switch__core {
    background-color: #667eea !important;
}

.el-tag {
    border-radius: 20px !important;
    padding: 4px 12px !important;
    font-weight: 500 !important;
}

.el-pagination {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 8px !important;
}

.el-pagination .el-pager li {
    border-radius: 8px !important;
    margin: 0 2px !important;
}

.el-pagination .el-pager li.is-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}